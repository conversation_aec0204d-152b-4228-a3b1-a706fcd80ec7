#!/usr/bin/env python3
"""Test the exact scenario that's failing in run.py."""

import os
import psutil
import sys

def test_run_py_detection():
    current_pid = os.getpid()
    print(f"Python executable: {sys.executable}")
    print(f"Current PID: {current_pid}")
    print("=" * 50)
    
    print("Simulating emergency shutdown logic:")
    processes_to_kill = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and any('n8n' in str(arg).lower() or 'run.py' in str(arg) for arg in cmdline):
                    is_current = proc.pid == current_pid
                    print(f"Found Python process: PID {proc.info['pid']}")
                    print(f"  Name: {proc.info['name']}")
                    print(f"  Cmdline: {cmdline}")
                    print(f"  Is current process: {is_current}")
                    print(f"  Would be killed: {not is_current}")
                    print()
                    
                    if not is_current:
                        processes_to_kill.append(proc.info['pid'])
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    print(f"Processes that would be killed: {processes_to_kill}")
    print(f"Current process would be preserved: {current_pid not in processes_to_kill}")

if __name__ == "__main__":
    test_run_py_detection()
