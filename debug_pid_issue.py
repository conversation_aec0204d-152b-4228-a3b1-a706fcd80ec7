#!/usr/bin/env python3
"""Debug the PID issue in detail."""

import os
import psutil
import sys
import time

def debug_pid_issue():
    current_pid = os.getpid()
    print(f"Script started with PID: {current_pid}")
    print(f"Python executable: {sys.executable}")
    print(f"Script name: {__file__}")
    print("=" * 50)
    
    # Check multiple times to see if PIDs change
    for i in range(3):
        print(f"\nCheck #{i+1}:")
        print(f"Current PID (os.getpid()): {os.getpid()}")
        
        # Find all processes with our script name
        matching_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
            try:
                cmdline = proc.info.get('cmdline', [])
                if cmdline and 'debug_pid_issue.py' in ' '.join(cmdline):
                    matching_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline,
                        'create_time': proc.info['create_time'],
                        'proc_pid': proc.pid  # Direct access to proc.pid
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        print(f"Found {len(matching_processes)} processes with our script:")
        for proc_info in matching_processes:
            is_current_info = proc_info['pid'] == current_pid
            is_current_proc = proc_info['proc_pid'] == current_pid
            print(f"  PID (info): {proc_info['pid']}, PID (proc): {proc_info['proc_pid']}")
            print(f"  Name: {proc_info['name']}")
            print(f"  Current (info): {is_current_info}, Current (proc): {is_current_proc}")
            print(f"  Create time: {time.ctime(proc_info['create_time'])}")
            print(f"  Command: {' '.join(proc_info['cmdline'])}")
            print()
        
        time.sleep(1)

if __name__ == "__main__":
    debug_pid_issue()
