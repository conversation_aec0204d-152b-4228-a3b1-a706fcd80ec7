#!/usr/bin/env python3
"""Test the precise N8N Builder process detection."""

import os
import psutil
import sys

def is_n8n_builder_process(proc):
    """Identify if a process belongs to N8N Builder specifically."""
    try:
        cmdline = proc.info.get('cmdline', [])
        if not cmdline:
            return False
            
        cmdline_str = ' '.join(cmdline).lower()
        
        # Check for specific N8N Builder patterns
        n8n_patterns = [
            'run.py',                    # Our main script
            'n8n_builder.app:app',       # Our FastAPI app
            'n8n_builder',               # Our module name
            'self_healer',               # Self-healer processes
        ]
        
        return any(pattern in cmdline_str for pattern in n8n_patterns)
        
    except Exception:
        return False

def test_precise_detection():
    current_pid = os.getpid()
    print(f"Python executable: {sys.executable}")
    print(f"Current PID: {current_pid}")
    print("=" * 50)
    
    print("Looking for N8N Builder processes:")
    found_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                if is_n8n_builder_process(proc):
                    is_current = proc.pid == current_pid
                    cmdline_str = ' '.join(proc.info.get('cmdline', []))
                    found_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline_str,
                        'is_current': is_current
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    if found_processes:
        for proc_info in found_processes:
            print(f"Found N8N Builder process:")
            print(f"  PID: {proc_info['pid']}")
            print(f"  Name: {proc_info['name']}")
            print(f"  Is current: {proc_info['is_current']}")
            print(f"  Command: {proc_info['cmdline']}")
            print()
    else:
        print("No N8N Builder processes found")
    
    print(f"Total N8N Builder processes: {len(found_processes)}")
    current_process_found = any(p['is_current'] for p in found_processes)
    print(f"Current process detected correctly: {current_process_found}")

if __name__ == "__main__":
    test_precise_detection()
