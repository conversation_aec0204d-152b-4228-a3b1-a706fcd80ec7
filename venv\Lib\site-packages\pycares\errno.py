from typing import <PERSON>

from ._cares import ffi as _ffi, lib as _lib
from .utils import maybe_str

ARES_SUCCESS = _lib.ARES_SUCCESS
# error codes
ARES_ENODATA = _lib.ARES_ENODATA
ARES_EFORMERR = _lib.ARES_EFORMERR
ARES_ESERVFAIL = _lib.ARES_ESERVFAIL
ARES_ENOTFOUND = _lib.ARES_ENOTFOUND
ARES_ENOTIMP = _lib.ARES_ENOTIMP
ARES_EREFUSED = _lib.ARES_EREFUSED
ARES_EBADQUERY = _lib.ARES_EBADQUERY
ARES_EBADNAME = _lib.ARES_EBADNAME
ARES_EBADFAMILY = _lib.ARES_EBADFAMILY
ARES_EBADRESP = _lib.ARES_EBADRESP
ARES_ECONNREFUSED = _lib.ARES_ECONNREFUSED
ARES_ETIMEOUT = _lib.ARES_ETIMEOUT
ARES_EOF = _lib.ARES_EOF
ARES_EFILE = _lib.ARES_EFILE
ARES_ENOMEM = _lib.ARES_ENOMEM
ARES_EDESTRUCTION = _lib.ARES_EDESTRUCTION
ARES_EBADSTR = _lib.ARES_EBADSTR
ARES_EBADFLAGS = _lib.ARES_EBADFLAGS
ARES_ENONAME = _lib.ARES_ENONAME
ARES_EBADHINTS = _lib.ARES_EBADHINTS
ARES_ENOTINITIALIZED = _lib.ARES_ENOTINITIALIZED
ARES_ELOADIPHLPAPI = _lib.ARES_ELOADIPHLPAPI
ARES_EADDRGETNETWORKPARAMS = _lib.ARES_EADDRGETNETWORKPARAMS
ARES_ECANCELLED = _lib.ARES_ECANCELLED
ARES_ESERVICE = _lib.ARES_ESERVICE

errorcode = {
    ARES_SUCCESS: "ARES_SUCCESS",
    # error codes
    ARES_ENODATA: "ARES_ENODATA",
    ARES_EFORMERR: "ARES_EFORMERR",
    ARES_ESERVFAIL: "ARES_ESERVFAIL",
    ARES_ENOTFOUND: "ARES_ENOTFOUND",
    ARES_ENOTIMP: "ARES_ENOTIMP",
    ARES_EREFUSED: "ARES_EREFUSED",
    ARES_EBADQUERY: "ARES_EBADQUERY",
    ARES_EBADNAME: "ARES_EBADNAME",
    ARES_EBADFAMILY: "ARES_EBADFAMILY",
    ARES_EBADRESP: "ARES_EBADRESP",
    ARES_ECONNREFUSED: "ARES_ECONNREFUSED",
    ARES_ETIMEOUT: "ARES_ETIMEOUT",
    ARES_EOF: "ARES_EOF",
    ARES_EFILE: "ARES_EFILE",
    ARES_ENOMEM: "ARES_ENOMEM",
    ARES_EDESTRUCTION: "ARES_EDESTRUCTION",
    ARES_EBADSTR: "ARES_EBADSTR",
    ARES_EBADFLAGS: "ARES_EBADFLAGS",
    ARES_ENONAME: "ARES_ENONAME",
    ARES_EBADHINTS: "ARES_EBADHINTS",
    ARES_ENOTINITIALIZED: "ARES_ENOTINITIALIZED",
    ARES_ELOADIPHLPAPI: "ARES_ELOADIPHLPAPI",
    ARES_EADDRGETNETWORKPARAMS: "ARES_EADDRGETNETWORKPARAMS",
    ARES_ECANCELLED: "ARES_ECANCELLED",
    ARES_ESERVICE: "ARES_ESERVICE",
}


def strerror(code: int) -> Union[str, bytes]:
    return maybe_str(_ffi.string(_lib.ares_strerror(code)))


__all__ = ("errorcode", "strerror", *errorcode.values())
