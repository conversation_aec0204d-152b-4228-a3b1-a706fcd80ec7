#!/usr/bin/env python3
"""Debug what processes are detected on our target ports."""

import psutil

def debug_port_check():
    target_ports = [8002, 8003, 8081]
    
    for port in target_ports:
        print(f"\nChecking port {port}:")
        found_processes = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                try:
                    connections = proc.info['connections']
                    if connections:
                        for conn in connections:
                            if hasattr(conn, 'laddr') and conn.laddr and conn.laddr.port == port:
                                found_processes.append({
                                    'pid': proc.info['pid'],
                                    'name': proc.info['name'],
                                    'connection': conn
                                })
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
        except Exception as e:
            print(f"Error checking port {port}: {e}")
            continue
        
        if found_processes:
            for proc_info in found_processes:
                print(f"  Found: PID {proc_info['pid']}, Name: {proc_info['name']}")
                print(f"    Connection: {proc_info['connection']}")
        else:
            print(f"  No processes found on port {port}")

if __name__ == "__main__":
    debug_port_check()
