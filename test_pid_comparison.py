#!/usr/bin/env python3
"""Test PID comparison logic specifically."""

import os
import psutil
import sys

def test_pid_comparison():
    current_pid = os.getpid()
    print(f"Python executable: {sys.executable}")
    print(f"Current PID from os.getpid(): {current_pid} (type: {type(current_pid)})")
    print("=" * 50)
    
    # Find our own process
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            proc_pid = proc.info['pid']
            if proc_pid == current_pid:
                print(f"Found our process:")
                print(f"  Process PID: {proc_pid} (type: {type(proc_pid)})")
                print(f"  Process name: {proc.info['name']}")
                print(f"  Process cmdline: {proc.info['cmdline']}")
                print(f"  PID comparison: {proc_pid} == {current_pid} = {proc_pid == current_pid}")
                print(f"  proc.pid: {proc.pid} (type: {type(proc.pid)})")
                print(f"  proc.pid == current_pid: {proc.pid == current_pid}")
                break
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    else:
        print("Could not find our own process!")

if __name__ == "__main__":
    test_pid_comparison()
