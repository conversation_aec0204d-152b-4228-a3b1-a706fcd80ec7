#!/usr/bin/env python3
"""Test script to compare process detection between system and venv Python."""

import os
import psutil
import sys

def test_process_detection():
    print(f"Python executable: {sys.executable}")
    print(f"Current PID: {os.getpid()}")
    print("=" * 50)
    
    print("Checking for python processes with 'n8n' or 'run.py' in cmdline:")
    found_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and any('n8n' in str(arg).lower() or 'run.py' in str(arg) for arg in cmdline):
                    is_current = proc.pid == os.getpid()
                    found_processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'cmdline': cmdline,
                        'is_current': is_current
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
        except Exception as e:
            print(f"Error checking process: {e}")
    
    if found_processes:
        for proc_info in found_processes:
            print(f"Found: PID {proc_info['pid']}, Name: {proc_info['name']}, Current: {proc_info['is_current']}")
            print(f"  Cmdline: {proc_info['cmdline']}")
            print()
    else:
        print("No matching processes found")
    
    print(f"Total matching processes: {len(found_processes)}")
    current_process_found = any(p['is_current'] for p in found_processes)
    print(f"Current process detected in list: {current_process_found}")

if __name__ == "__main__":
    test_process_detection()
